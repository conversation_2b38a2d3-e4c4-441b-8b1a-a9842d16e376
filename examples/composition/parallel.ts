import { allSteps, defineStep } from "../../packages/core/mod.ts";
import { createStdEngine } from "../../packages/std/mod.ts";
import type { Meta } from "../../packages/core/types.ts";

type Base = { userId: string };

const fetchUserData = defineStep<Base>()({
  name: "fetch-user-data",
  meta: {
    db: { role: "ro" },
    log: { level: "debug" },
  } satisfies Meta,
  async run({ userId, log }) {
    log.info("fetching.user.data", { userId });
    return { id: userId, name: "<PERSON>" };
  },
});

const fetchOrders = defineStep<Base>()({
  name: "fetch-orders",
  meta: {
    db: { role: "ro" },
    log: { level: "debug" },
  } satisfies Meta,
  async run({ userId, log }) {
    log.info("fetching.orders", { userId });
    return [{ orderId: "o1", total: 100 }, { orderId: "o2", total: 200 }];
  },
});

const fetchRecommendations = defineStep<Base>()({
  name: "fetch-recommendations",
  meta: {
    http: { baseUrl: "https://api.example.com" },
    log: { level: "debug" },
  } satisfies Meta,
  async run({ userId, log }) {
    log.info("fetching.recommendations", { userId });
    return ["item1", "item2", "item3"];
  },
});

const parallelFetch = allSteps<Base>()(
  fetchUserData,
  fetchOrders,
  fetchRecommendations,
);

console.log("\n=== Running Parallel Execution Example ===\n");

const engine = createStdEngine<Base>();
const [userData, orders, recommendations] = await engine.run(
  parallelFetch,
  { userId: "456" },
);

console.log("User:", userData);
console.log("Orders:", orders);
console.log("Recommendations:", recommendations);
